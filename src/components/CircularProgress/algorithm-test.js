// 测试 option.js 的算法逻辑

function testAlgorithm() {
  console.log('=== option.js 算法测试 ===');
  
  const testValues = [0, 25, 50, 75, 80, 90, 100];
  
  testValues.forEach(value => {
    // 按照 option.js 的算法
    const progressValue = (75 * value) / 100;
    const remainingValue = 100 - progressValue;
    
    console.log(`进度: ${value}%`);
    console.log(`  前景进度值: ${progressValue.toFixed(2)}`);
    console.log(`  前景剩余值: ${remainingValue.toFixed(2)}`);
    console.log(`  前景总和: ${(progressValue + remainingValue).toFixed(2)}`);
    console.log(`  背景层固定: 75 + 25 = 100`);
    
    if (progressValue > 75) {
      console.log(`  ⚠️  警告: 前景进度值 ${progressValue.toFixed(2)} 超过背景层范围 75`);
    }
    console.log('---');
  });
}

// 分析问题
function analyzeIssue() {
  console.log('\n=== 问题分析 ===');
  console.log('当进度 > 75% 时：');
  console.log('- 前景进度值会超过 75');
  console.log('- 但背景层只显示 75 的范围');
  console.log('- 这会导致进度条超出背景范围');
  
  console.log('\n可能的解决方案：');
  console.log('1. 限制前景进度值不超过 75');
  console.log('2. 调整背景层的范围');
  console.log('3. 重新理解 option.js 的设计意图');
}

// 测试限制方案
function testLimitedAlgorithm() {
  console.log('\n=== 限制方案测试 ===');
  
  const testValues = [0, 25, 50, 75, 80, 90, 100];
  
  testValues.forEach(value => {
    // 限制前景进度值不超过 75
    const rawProgressValue = (75 * value) / 100;
    const progressValue = Math.min(rawProgressValue, 75);
    const remainingValue = 100 - progressValue;
    
    console.log(`进度: ${value}%`);
    console.log(`  原始进度值: ${rawProgressValue.toFixed(2)}`);
    console.log(`  限制后进度值: ${progressValue.toFixed(2)}`);
    console.log(`  剩余值: ${remainingValue.toFixed(2)}`);
    console.log(`  是否被限制: ${rawProgressValue > 75 ? '是' : '否'}`);
    console.log('---');
  });
}

// 运行测试
testAlgorithm();
analyzeIssue();
testLimitedAlgorithm();
