<template>
  <div class="model-call-trend bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 标题和筛选控件 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-gray-800">模型调用趋势</h3>
      <div class="flex items-center space-x-2">
        <a-button
          v-for="option in timeOptions"
          :key="option.value"
          :type="selectedTimeRange === option.value ? 'primary' : 'default'"
          size="small"
          @click="handleTimeRangeChange(option.value)"
        >
          {{ option.label }}
        </a-button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { Button as AButton } from 'ant-design-vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { selectModelServerTrend } from '@/api/security/modelStats';
  import type { BiModelServerApiCallVo } from '@/api/security/modelStats/model';

  defineOptions({ name: 'ModelCallTrend' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<BiModelServerApiCallVo[]>([]);
  const selectedTimeRange = ref('2'); // 默认按天

  const timeOptions = [
    { label: '按小时', value: '1' },
    { label: '按天', value: '2' },
    { label: '按周', value: '3' },
  ];

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 获取图表数据
  async function fetchChartData() {
    try {
      loading.value = true;
      const params = {
        trentDimension: selectedTimeRange.value,
      };

      const response = await selectModelServerTrend(params);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取模型调用趋势数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    if (!chartData.value.length) {
      setOptions({
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#999',
            fontSize: 14,
          },
        },
      });
      return;
    }

    // 处理数据 - 按模型分组
    const modelMap = new Map<
      string,
      Map<string, { success: number; failure: number; total: number }>
    >();
    const allTimes = new Set<string>();

    chartData.value.forEach((item) => {
      const time = item.collectTime || '';
      const modelName = item.modelName || '未知模型';
      const success = item.callSuccess || 0;
      const failure = item.callFailure || 0;
      const total = success + failure;

      allTimes.add(time);

      // 按模型统计
      if (!modelMap.has(modelName)) {
        modelMap.set(modelName, new Map());
      }
      const modelTimeMap = modelMap.get(modelName)!;
      if (!modelTimeMap.has(time)) {
        modelTimeMap.set(time, { success: 0, failure: 0, total: 0 });
      }
      const modelTimeData = modelTimeMap.get(time)!;
      modelTimeData.success += success;
      modelTimeData.failure += failure;
      modelTimeData.total += total;
    });

    // 准备图表数据 - 时间轴排序
    const times = Array.from(allTimes).sort();

    // 定义颜色数组
    const colors = [
      '#3b82f6',
      '#f59e0b',
      '#10b981',
      '#ef4444',
      '#8b5cf6',
      '#f97316',
      '#06b6d4',
      '#84cc16',
      '#ec4899',
      '#6366f1',
    ];

    // 为每个模型创建系列数据
    const modelNames = Array.from(modelMap.keys());
    const series: any[] = [];
    const legendData: string[] = [];

    modelNames.forEach((modelName, index) => {
      const modelTimeMap = modelMap.get(modelName)!;
      const modelData = times.map((time) => modelTimeMap.get(time)?.total || 0);
      const color = colors[index % colors.length];

      legendData.push(modelName);
      series.push({
        name: modelName,
        type: 'line',
        data: modelData,
        smooth: true,
        lineStyle: {
          color: color,
          width: 2,
        },
        itemStyle: {
          color: color,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: color.replace(')', ', 0.3)').replace('rgb', 'rgba') },
              { offset: 1, color: color.replace(')', ', 0.05)').replace('rgb', 'rgba') },
            ],
          },
        },
      });
    });

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        borderRadius: 8,
        textStyle: {
          color: '#374151',
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            const modelName = param.seriesName;
            const time = param.axisValue;
            const modelTimeMap = modelMap.get(modelName);
            const data = modelTimeMap?.get(time);
            if (data) {
              result += `${param.marker}${modelName}: ${param.value} (成功: ${data.success}, 失败: ${data.failure})<br/>`;
            } else {
              result += `${param.marker}${modelName}: ${param.value}<br/>`;
            }
          });
          return result;
        },
      },
      legend: {
        data: legendData,
        top: 10,
        right: 20,
        type: 'scroll',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLabel: {
          rotate: 45,
          formatter: function (value: string) {
            // 根据时间维度格式化显示
            if (selectedTimeRange.value === '1') {
              // 按小时：显示 MM-dd HH
              return value.substring(5);
            } else if (selectedTimeRange.value === '2') {
              // 按天：显示 MM-dd
              return value.substring(5, 10);
            } else {
              // 按周：显示完整日期
              return value;
            }
          },
        },
        axisTick: {
          lineStyle: {
            color: '#e5e7eb',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '调用次数',
        nameTextStyle: {
          color: '#6b7280',
        },
        axisLabel: {
          formatter: '{value}',
          color: '#6b7280',
        },
        axisLine: {
          lineStyle: {
            color: '#e5e7eb',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
          },
        },
      },
      series: series,
    };

    setOptions(option as any);
  }

  // 时间维度变化处理
  function handleTimeRangeChange(value: string) {
    selectedTimeRange.value = value;
    fetchChartData();
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    () => {
      fetchChartData();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchChartData();
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => fetchChartData(),
  });
</script>

<style scoped>
  /* 图表容器样式 */
</style>
